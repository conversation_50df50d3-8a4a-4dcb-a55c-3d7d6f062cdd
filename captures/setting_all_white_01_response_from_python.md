0000   00 7e e1 81 6c 9b ff ff 43 02 00 03 01 00 2d 3e   .~..l...C.....->
0010   ef 53 04 67 00 00 00 00 cc f7 04 00 00 00 00 00   .S.g............
0020   08 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0030   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................


Frame 4744: 64 bytes on wire (512 bits), 64 bytes captured (512 bits) on interface usbmon1, id 0
    Section number: 1
    Interface id: 0 (usbmon1)
        Interface name: usbmon1
    Encapsulation type: USB packets with Linux header and padding (115)
    Arrival Time: Oct  7, 2024 23:34:39.325580000 CEST
    UTC Arrival Time: Oct  7, 2024 21:34:39.325580000 UTC
    Epoch Arrival Time: 1728336879.325580000
    [Time shift for this packet: 0.000000000 seconds]
    [Time delta from previous captured frame: 0.000144000 seconds]
    [Time delta from previous displayed frame: 0.000144000 seconds]
    [Time since reference or first frame: 141.787859000 seconds]
    Frame Number: 4744
    Frame Length: 64 bytes (512 bits)
    Capture Length: 64 bytes (512 bits)
    [Frame is marked: False]
    [Frame is ignored: False]
    [Protocols in frame: usb]
USB URB
    [Source: 1.3.0]
    [Destination: host]
    URB id: 0xffff9b6c81e17e00
    URB type: URB_COMPLETE ('C')
    URB transfer type: URB_CONTROL (0x02)
    Endpoint: 0x00, Direction: OUT
        0... .... = Direction: OUT (0)
        .... 0000 = Endpoint number: 0
    Device: 3
    URB bus id: 1
    Device setup request: not relevant ('-')
    Data: not present ('>')
    URB sec: 1728336879
    URB usec: 325580
    URB status: Success (0)
    URB length [bytes]: 8
    Data length [bytes]: 0
    [Request in: 4743]
    [Time from request: 0.000144000 seconds]
    Unused Setup Header
    Interval: 0
    Start frame: 0
    Copy of Transfer Flags: 0x00000000
    Number of ISO descriptors: 0
    [bInterfaceClass: HID (0x03)]
