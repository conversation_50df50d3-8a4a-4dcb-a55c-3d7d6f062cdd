0000   1b 00 a0 a9 e1 e5 84 ae ff ff 00 00 00 00 09 00   ................
0010   00 01 00 03 00 04 01 40 00 00 00 00 ff ff ff 00   .......@........
0020   ff ff ff 00 ff ff ff 00 ff ff ff 00 ff ff ff 00   ................
0030   ff ff ff 00 ff ff ff 00 ff ff ff 00 ff ff ff 00   ................
0040   ff ff ff 00 ff ff ff 00 ff ff ff 00 ff ff ff 00   ................
0050   ff ff ff 00 ff ff ff 00 ff ff ff                  ...........


Frame 2895: 91 bytes on wire (728 bits), 91 bytes captured (728 bits) on interface \\.\USBPcap1, id 0
    Section number: 1
    Interface id: 0 (\\.\USBPcap1)
        Interface name: \\.\USBPcap1
        Interface description: USBPcap1
    Encapsulation type: USB packets with USBPcap header (152)
    Arrival Time: Oct  6, 2024 00:55:23.758921000 CEST
    UTC Arrival Time: Oct  5, 2024 22:55:23.758921000 UTC
    Epoch Arrival Time: 1728168923.758921000
    [Time shift for this packet: 0.000000000 seconds]
    [Time delta from previous captured frame: 0.030251000 seconds]
    [Time delta from previous displayed frame: 0.030251000 seconds]
    [Time since reference or first frame: 42.626163000 seconds]
    Frame Number: 2895
    Frame Length: 91 bytes (728 bits)
    Capture Length: 91 bytes (728 bits)
    [Frame is marked: False]
    [Frame is ignored: False]
    [Protocols in frame: usb:usbhid]
USB URB
    [Source: host]
    [Destination: 1.3.4]
    USBPcap pseudoheader length: 27
    IRP ID: 0xffffae84e5e1a9a0
    IRP USBD_STATUS: USBD_STATUS_SUCCESS (0x00000000)
    URB Function: URB_FUNCTION_BULK_OR_INTERRUPT_TRANSFER (0x0009)
    IRP information: 0x00, Direction: FDO -> PDO
        0000 000. = Reserved: 0x00
        .... ...0 = Direction: FDO -> PDO (0x0)
    URB bus id: 1
    Device address: 3
    Endpoint: 0x04, Direction: OUT
        0... .... = Direction: OUT (0)
        .... 0100 = Endpoint number: 4
    URB transfer type: URB_INTERRUPT (0x01)
    Packet Data Length: 64
    [Response in: 2896]
    [bInterfaceClass: HID (0x03)]
HID Data: 00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff00ffffff
