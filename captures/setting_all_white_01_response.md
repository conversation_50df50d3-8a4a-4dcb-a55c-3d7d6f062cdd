0000   1c 00 a0 19 04 e0 84 ae ff ff 00 00 00 00 08 00   ................
0010   01 01 00 03 00 00 02 00 00 00 00 03               ............


Frame 2894: 28 bytes on wire (224 bits), 28 bytes captured (224 bits) on interface \\.\USBPcap1, id 0
    Section number: 1
    Interface id: 0 (\\.\USBPcap1)
        Interface name: \\.\USBPcap1
        Interface description: USBPcap1
    Encapsulation type: USB packets with USBPcap header (152)
    Arrival Time: Oct  6, 2024 00:55:23.728670000 CEST
    UTC Arrival Time: Oct  5, 2024 22:55:23.728670000 UTC
    Epoch Arrival Time: 1728168923.728670000
    [Time shift for this packet: 0.000000000 seconds]
    [Time delta from previous captured frame: 0.000339000 seconds]
    [Time delta from previous displayed frame: 0.000339000 seconds]
    [Time since reference or first frame: 42.595912000 seconds]
    Frame Number: 2894
    Frame Length: 28 bytes (224 bits)
    Capture Length: 28 bytes (224 bits)
    [Frame is marked: False]
    [Frame is ignored: False]
    [Protocols in frame: usb]
USB URB
    [Source: 1.3.0]
    [Destination: host]
    USBPcap pseudoheader length: 28
    IRP ID: 0xffffae84e00419a0
    IRP USBD_STATUS: USBD_STATUS_SUCCESS (0x00000000)
    URB Function: URB_FUNCTION_CONTROL_TRANSFER (0x0008)
    IRP information: 0x01, Direction: PDO -> FDO
        0000 000. = Reserved: 0x00
        .... ...1 = Direction: PDO -> FDO (0x1)
    URB bus id: 1
    Device address: 3
    Endpoint: 0x00, Direction: OUT
        0... .... = Direction: OUT (0)
        .... 0000 = Endpoint number: 0
    URB transfer type: URB_CONTROL (0x02)
    Packet Data Length: 0
    [Request in: 2893]
    [Time from request: 0.000339000 seconds]
    Control transfer stage: Complete (3)
    [bInterfaceClass: HID (0x03)]
