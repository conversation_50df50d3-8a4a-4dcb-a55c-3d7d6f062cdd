[project]
name = "rgbkb"
version = "0.0.1"
authors = [
  { name="Ondre<PERSON>", email="<EMAIL>" },
]
description = "App and library to control RGB keyboard backlight"
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: Public Domain",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Operating System :: POSIX :: Linux",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB)",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Human Interface Device (HID)",
    "Topic :: Utilities",
    "Typing :: Typed",
    "Topic :: System",
    "Topic :: System :: Hardware",
    "Topic :: System :: Hardware :: Universal Serial Bus (USB) :: Miscellaneous",
    "Topic :: Multimedia :: Graphics :: Presentation",
    "Natural Language :: English"
]
dependencies = [
    "usbx==0.8.1"
]

[project.urls]
Homepage = "https://github.com/fuho/rgbkb"
Issues = "https://github.com/fuho/rgbkb/issues"

[project.optional-dependencies]
dev = [
  "pytest",
]

[project.scripts]
rgbkb = "rgbkb.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "--import-mode=importlib",
    "-rpP"
]
testpaths = [
    "tests"
]