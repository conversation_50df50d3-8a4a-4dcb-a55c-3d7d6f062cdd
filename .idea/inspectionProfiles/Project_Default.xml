<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="5">
            <item index="0" class="java.lang.String" itemvalue="bresenham" />
            <item index="1" class="java.lang.String" itemvalue="matplotlib" />
            <item index="2" class="java.lang.String" itemvalue="anytree" />
            <item index="3" class="java.lang.String" itemvalue="numpy" />
            <item index="4" class="java.lang.String" itemvalue="astar" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>